"""
Webpage Element Extractor

This script extracts the HTML and style information of multiple webpage elements
located at or within the specified bounding box coordinates and returns
a structured output.
"""

import argparse
import json
import os
import time
from typing import Dict, Any, Optional, Tuple, List

import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager


class WebpageElementExtractor:
    """Extract HTML and style information from a webpage element."""

    def __init__(self, headless: bool = True, timeout: int = 30, screenshot_width: Optional[int] = None, screenshot_height: Optional[int] = None):
        """
        Initialize the WebpageElementExtractor.

        Args:
            headless: Whether to run Chrome in headless mode
            timeout: Maximum time to wait for page load in seconds
            screenshot_width: Width of the screenshot (for matching resolution)
            screenshot_height: Height of the screenshot (for matching resolution)
        """
        self.driver = None
        self.headless = headless
        self.timeout = timeout
        self.screenshot_width = screenshot_width
        self.screenshot_height = screenshot_height

    def setup_driver(self):
        """Setup and configure the Chrome WebDriver."""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")

        # Set window size to match screenshot resolution
        if self.screenshot_width and self.screenshot_height:
            chrome_options.add_argument(f"--window-size={self.screenshot_width},{self.screenshot_height}")
        else:
            chrome_options.add_argument("--window-size=1920,1080") #Default

        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        # Setup Chrome driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.set_page_load_timeout(self.timeout)

    def get_page(self, url: str) -> bool:
        """
        Load the webpage.

        Args:
            url: The URL of the webpage to load

        Returns:
            bool: True if page loaded successfully, False otherwise
        """
        try:
            self.driver.get(url)
            # Wait for page to load
            time.sleep(3)
            return True
        except Exception as e:
            print(f"Error loading page: {e}")
            return False

    def find_element_at_position(self, x: int, y: int) -> Optional[Dict[str, Any]]:
        """
        Find the element at the specified coordinates.

        Args:
            x: X-coordinate in pixels
            y: Y-coordinate in pixels

        Returns:
            Dict containing element information or None if not found
        """
        script = """
        function getElementAtPosition(x, y) {
            const element = document.elementFromPoint(x, y);
            if (!element) return null;

            // Get computed styles
            const computedStyle = window.getComputedStyle(element);
            const computedStyleObj = {};
            for (let i = 0; i < computedStyle.length; i++) {
                const prop = computedStyle[i];
                computedStyleObj[prop] = computedStyle.getPropertyValue(prop);
            }

            // Get element attributes
            const attributes = {};
            for (let i = 0; i < element.attributes.length; i++) {
                const attr = element.attributes[i];
                attributes[attr.name] = attr.value;
            }

            // Get element XPath
            function getXPath(element) {
                if (element.id !== '')
                    return `//*[@id="${element.id}"]`;

                if (element === document.body)
                    return '/html/body';

                let ix = 0;
                const siblings = element.parentNode.childNodes;

                for (let i = 0; i < siblings.length; i++) {
                    const sibling = siblings[i];
                    if (sibling === element)
                        return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                        ix++;
                }
            }

            // Get CSS selector
            function getCssSelector(element) {
                if (element.id) return '#' + element.id;
                if (element.className) {
                    const classes = Array.from(element.classList).join('.');
                    return element.tagName.toLowerCase() + (classes ? '.' + classes : '');
                }

                let path = '';
                while (element && element.nodeType === 1) {
                    let selector = element.tagName.toLowerCase();

                    if (element.id) {
                        selector += '#' + element.id;
                        path = selector + (path ? ' > ' + path : '');
                        break;
                    } else {
                        let siblings = element.parentNode ? element.parentNode.childNodes : [];
                        let index = 1;

                        for (let i = 0; i < siblings.length; i++) {
                            let sibling = siblings[i];
                            if (sibling === element) break;
                            if (sibling.nodeType === 1 && sibling.tagName === element.tagName) index++;
                        }

                        if (index > 1) selector += ':nth-of-type(' + index + ')';
                    }

                    path = selector + (path ? ' > ' + path : '');
                    element = element.parentNode;
                }

                return path;
            }

            return {
                tag: element.tagName.toLowerCase(),
                id: element.id || null,
                classes: element.className ? element.className.split(' ').filter(c => c.length > 0) : [],
                attributes: attributes,
                text: element.textContent.trim(),
                href: element.href || null,
                src: element.src || null,
                inlineStyle: element.style.cssText || null,
                computedStyle: computedStyleObj,
                xpath: getXPath(element),
                cssSelector: getCssSelector(element)
            };
        }
        return getElementAtPosition(arguments[0], arguments[1]);
        """
        try:
            return self.driver.execute_script(script, x, y)
        except Exception as e:
            print(f"Error finding element at position: {e}")
            return None

    def find_element_in_bounding_box(self, x: int, y: int, width: int, height: int, target_tag: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Find the most relevant element within the specified bounding box,
        with preference for specific elements based on target_tag if provided.

        Args:
            x: Top-left X-coordinate of bounding box
            y: Top-left Y-coordinate of bounding box
            width: Width of bounding box
            height: Height of bounding box
            target_tag: (Optional) If specified, focuses the search on this specific tag

        Returns:
            Dict containing element information or None if not found
        """
        script = """
        function getElementsInBoundingBox(x, y, width, height, targetTag) {
            const elements = [];
            const allElements = document.querySelectorAll('*');

            for (const element of allElements) {
                const rect = element.getBoundingClientRect();

                // Check if element overlaps with bounding box
                if (rect.left < (x + width) &&
                    (rect.left + rect.width) > x &&
                    rect.top < (y + height) &&
                    (rect.top + rect.height) > y) {

                    // Calculate overlap area
                    const overlapX = Math.max(0, Math.min(x + width, rect.left + rect.width) - Math.max(x, rect.left));
                    const overlapY = Math.max(0, Math.min(y + height, rect.top + rect.height) - Math.max(y, rect.top));
                    const overlapArea = overlapX * overlapY;
                    const elementArea = rect.width * rect.height;

                    let elementTag = element.tagName.toLowerCase();
                    let isTargetTag = (!targetTag || elementTag === targetTag.toLowerCase());  // Match target tag

                    elements.push({
                        element: element,
                        tag: elementTag,
                        overlapArea: overlapArea,
                        elementArea: elementArea,
                        overlapPercentage: overlapArea / elementArea,
                        depth: getElementDepth(element),
                        isTargetTag: isTargetTag  // Flag if it's the target tag
                    });
                }
            }

            return elements;
        }

        function getElementDepth(element) {
            let depth = 0;
            let current = element;
            while (current) {
                depth++;
                current = current.parentElement;
            }
            return depth;
        }

        function getElementInfo(element) {
            // Get computed styles
            const computedStyle = window.getComputedStyle(element);
            const computedStyleObj = {};
            for (let i = 0; i < computedStyle.length; i++) {
                const prop = computedStyle[i];
                computedStyleObj[prop] = computedStyle.getPropertyValue(prop);
            }

            // Get element attributes
            const attributes = {};
            for (let i = 0; i < element.attributes.length; i++) {
                const attr = element.attributes[i];
                attributes[attr.name] = attr.value;
            }

            // Get element XPath
            function getXPath(element) {
                if (element.id !== '')
                    return `//*[@id="${element.id}"]`;

                if (element === document.body)
                    return '/html/body';

                let ix = 0;
                const siblings = element.parentNode.childNodes;

                for (let i = 0; i < siblings.length; i++) {
                    const sibling = siblings[i];
                    if (sibling === element)
                        return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                        ix++;
                }
            }

            // Get CSS selector
            function getCssSelector(element) {
                if (element.id) return '#' + element.id;
                if (element.className) {
                    const classes = Array.from(element.classList).join('.');
                    return element.tagName.toLowerCase() + (classes ? '.' + classes : '');
                }

                let path = '';
                while (element && element.nodeType === 1) {
                    let selector = element.tagName.toLowerCase();

                    if (element.id) {
                        selector += '#' + element.id;
                        path = selector + (path ? ' > ' + path : '');
                        break;
                    } else {
                        let siblings = element.parentNode ? element.parentNode.childNodes : [];
                        let index = 1;

                        for (let i = 0; i < siblings.length; i++) {
                            let sibling = siblings[i];
                            if (sibling === element) break;
                            if (sibling.nodeType === 1 && sibling.tagName === element.tagName) index++;
                        }

                        if (index > 1) selector += ':nth-of-type(' + index + ')';
                    }

                    path = selector + (path ? ' > ' + path : '');
                    element = element.parentNode;
                }

                return path;
            }

            return {
                tag: element.tagName.toLowerCase(),
                id: element.id || null,
                classes: element.className ? element.className.split(' ').filter(c => c.length > 0) : [],
                attributes: attributes,
                text: element.textContent.trim(),
                href: element.href || null,
                src: element.src || null,
                inlineStyle: element.style.cssText || null,
                computedStyle: computedStyleObj,
                xpath: getXPath(element),
                cssSelector: getCssSelector(element)
            };
        }

        // Find elements in the bounding box
        const elementsInBox = getElementsInBoundingBox(arguments[0], arguments[1], arguments[2], arguments[3], arguments[4]);

        if (elementsInBox.length === 0) {
            return null;
        }

        // If a target tag is specified, prioritize that tag
        if (targetTag) {
            let targetElements = elementsInBox.filter(el => el.isTargetTag);
            if (targetElements.length > 0) {
                // Prioritize deeper elements, then by overlap, then by smaller area
                targetElements.sort((a, b) => {
                    if (b.depth !== a.depth) {
                        return b.depth - a.depth;  // Deeper elements first
                    }
                    if (b.overlapPercentage !== a.overlapPercentage) {
                        return b.overlapPercentage - a.overlapPercentage; // Higher overlap first
                    }
                    return a.elementArea - b.elementArea;  // Smaller area first
                });
                return getElementInfo(targetElements[0].element);
            }
        }


        //Sort by depth, overlap and area:

        elementsInBox.sort((a,b)=>{
            if (b.depth !== a.depth) {
                return b.depth - a.depth;  // Deeper elements first
            }
            if (b.overlapPercentage !== a.overlapPercentage) {
                return b.overlapPercentage - a.overlapPercentage; // Higher overlap first
            }
            return a.elementArea - b.elementArea;  // Smaller area first

        })


        return getElementInfo(elementsInBox[0].element);
        """
        try:
            element_info = self.driver.execute_script(script, x, y, width, height, target_tag)
            return element_info
        except Exception as e:
            print(f"Error finding element in bounding box: {e}")

            # Fallback to the original point-based method if the new approach fails
            center_x = x + width // 2
            center_y = y + height // 2

            # Try center point first
            element = self.find_element_at_position(center_x, center_y)
            if element:
                return element

            # If center point fails, try corners and edges
            points = [
                (x, y),  # Top-left
                (x + width, y),  # Top-right
                (x, y + height),  # Bottom-left
                (x + width, y + height),  # Bottom-right
                (center_x, y),  # Top-center
                (x, center_y),  # Left-center
                (center_x, y + height),  # Bottom-center
                (x + width, center_y)  # Right-center
            ]

            for px, py in points:
                element = self.find_element_at_position(px, py)
                if element:
                    return element

            return None

    def visualize_bounding_box(self, screenshot_path: str,
                               x: int, y: int, width: int, height: int,
                               output_path: str = "output_visualization.png"):
        """
        Create a visualization of the bounding box on the screenshot.

        Args:
            screenshot_path: Path to the screenshot image
            x, y, width, height: Bounding box coordinates
            output_path: Path to save the visualization
        """
        # Read screenshot
        if not os.path.exists(screenshot_path):
            print(f"Screenshot file not found: {screenshot_path}")
            return

        img = cv2.imread(screenshot_path)
        if img is None:
            print(f"Failed to read screenshot: {screenshot_path}")
            return

        # Draw bounding box
        color = (0, 255, 0)  # Green
        thickness = 2
        img = cv2.rectangle(img, (x, y), (x + width, y + height), color, thickness)

        # Add center point
        center_x = x + width // 2
        center_y = y + height // 2
        img = cv2.circle(img, (center_x, center_y), 5, (0, 0, 255), -1)

        # Save the visualization
        cv2.imwrite(output_path, img)
        print(f"Visualization saved to: {output_path}")

    def extract_multiple_elements_info(self, url: str, screenshot_path: str,
                                     elements_data: List[Dict[str, Any]],
                                     visualize: bool = True) -> Dict[str, Any]:
        """
        Extract information about multiple elements based on provided coordinates.

        Args:
            url: The webpage URL
            screenshot_path: Path to the screenshot image
            elements_data: A list of dictionaries, where each dictionary contains
                           'x', 'y', 'width', 'height', and optionally 'target_tag'.
            visualize: Whether to create a visualization image

        Returns:
            A dictionary where keys are element identifiers (e.g., "element_1", "element_2")
            and values are dictionaries containing element information.
        """
        try:
            if self.driver is None:
                self.setup_driver()

            success = self.get_page(url)
            if not success:
                return {"error": "Failed to load page"}

            results = {}
            for i, element_data in enumerate(elements_data):
                x = element_data['x']
                y = element_data['y']
                width = element_data['width']
                height = element_data['height']
                target_tag = element_data.get('target_tag')  # Optional target tag

                element_info = self.find_element_in_bounding_box(x, y, width, height, target_tag)

                if visualize and os.path.exists(screenshot_path):
                    self.visualize_bounding_box(screenshot_path, x, y, width, height,
                                                 output_path=f"output_visualization_{i + 1}.png") #Unique names for each visualization

                if element_info:
                    # Add HTML content of the element
                    try:
                        if "xpath" in element_info:
                            outer_html_script = f"""
                            function getElementHTML(xpath) {{
                                const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                                return element ? element.outerHTML : null;
                            }}
                            return getElementHTML("{element_info['xpath']}");
                            """
                            element_html = self.driver.execute_script(outer_html_script)
                            if element_html:
                                element_info['outerHTML'] = element_html
                    except Exception as e:
                        print(f"Error getting element HTML: {e}")

                    results[f"element_{i + 1}"] = element_info
                else:
                    results[f"element_{i + 1}"] = {"error": f"No {target_tag if target_tag else 'element'} found in the specified bounding box"}

        except Exception as e:
            return {"error": f"Error extracting element info: {str(e)}"}
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None
        return results


def main():
    """Parse command line arguments and run the element extractor."""
    parser = argparse.ArgumentParser(description="Extract HTML and style information from multiple webpage elements")
    parser.add_argument("url", help="The webpage URL")
    parser.add_argument("screenshot", help="Path to the webpage screenshot")
    parser.add_argument("--elements_file", type=str, required=True,
                        help="Path to a JSON file containing a list of element coordinates and target tags.")
    parser.add_argument("--output", default="elements_info.json", help="Output JSON file")
    parser.add_argument("--no-visualize", action="store_true", help="Disable visualization")

    args = parser.parse_args()

    # Get the screenshot width and height by reading the image
    try:
        img = cv2.imread(args.screenshot)
        screenshot_height, screenshot_width, _ = img.shape
    except Exception as e:
        print(f"Error reading image: {e}.  Please ensure the path is correct.")
        screenshot_width = None
        screenshot_height = None

    extractor = WebpageElementExtractor(screenshot_width=screenshot_width, screenshot_height=screenshot_height)

    try:
        with open(args.elements_file, 'r', encoding='utf-8') as f:
            elements_data = json.load(f)
        if not isinstance(elements_data, list):
            raise ValueError("Elements data must be a JSON list in the file.")
    except json.JSONDecodeError as e:
        print(f"Error decoding elements JSON: {e}")
        return
    except ValueError as e:
        print(e)
        return
    except FileNotFoundError:
        print(f"Error: The file '{args.elements_file}' was not found.")
        return

    result = extractor.extract_multiple_elements_info(
        args.url,
        args.screenshot,
        elements_data,
        not args.no_visualize
    )

    # Save output to JSON file
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"Element information saved to {args.output}")
    print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()